package com.jly.sharding.adapter.algorithm;

import com.jly.sharding.adapter.chain.DatasourcePostprocessChain;
import com.jly.sharding.adapter.chain.ProcessChainManager;
import com.jly.sharding.adapter.context.ShardingDetermineContext;
import com.jly.sharding.adapter.dto.DatasourceShardingDetermines;
import com.jly.sharding.adapter.dto.ShardingDetermines;
import com.jly.sharding.adapter.enums.DatasourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.sharding.api.sharding.hint.HintShardingValue;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-08-14
 */
@Slf4j
public class DatasourceHintShardingAlgorithm extends AbstractHintShardingAlgorithm {

    @Override
    public String getType() {
        return "HINT_FB_ACCESS_DATASOURCE";
    }

    public static final Map<String, Set<String>> slaveNames = new HashMap<>();


    @Override
    protected Collection<String> shardingDetermines(Set<String> availableTargetNames, ShardingDetermines determines, HintShardingValue<String> shardingValue) {
        if (availableTargetNames.isEmpty()) {
            this.buildSalveNames(availableTargetNames);
        }
        // 将算法获取到的值设置的pending中
        DatasourceShardingDetermines databaseDetermines = determines.getDatabaseDetermines();
        // 待处理值, 如果处理链条不处理则取默认值
        String invokeSimpleInfo = ShardingDetermineContext.getInvokeSimpleInfo();
        String pendingValue = shardingValue.getValues().iterator().next();
        if (pendingValue == null) {
            log.error("No datasource sharding value found for method: {}", invokeSimpleInfo);
            throw new RuntimeException("No datasource sharding value found for method: " + invokeSimpleInfo);
        }
        databaseDetermines.setPendingValue(pendingValue);
        databaseDetermines.setFinalName(pendingValue);
        databaseDetermines.setAvailableNames(availableTargetNames);
        // 执行后置处理器链条 扩展点
        ProcessChainManager.process(determines, DatasourcePostprocessChain.class);

        // 提取最终表名
        String finalDatasourceName = databaseDetermines.getFinalName();
        if (finalDatasourceName == null) {
            log.error("No final datasource name found for method: {}", invokeSimpleInfo);
            throw new RuntimeException("No final datasource name found for method: " + invokeSimpleInfo);
        }
        // 最终获取的库名和可用库名不匹配
        if (!availableTargetNames.contains(pendingValue)) {
            log.error("No available datasource sharding value found for method: {}", invokeSimpleInfo);
            throw new RuntimeException("No available datasource sharding value found for method: " + invokeSimpleInfo);
        }

        log.info("HintShardingAlgorithm the actual database executed: {},{},{}", this.getType(), finalDatasourceName, invokeSimpleInfo);
        return Collections.singleton(finalDatasourceName);
    }

    private void buildSalveNames(Set<String> availableTargetNames) {
        for (String availableTargetName : availableTargetNames) {
            String suffix = DatasourceType.Slave.getSuffix();
            if (availableTargetName.contains(suffix)) {
                slaveNames.put(availableTargetName.substring(suffix, ""), availableTargetNames);
            }
        }
    }
}
